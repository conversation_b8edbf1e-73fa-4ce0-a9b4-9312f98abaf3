import 'package:get/get.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'dart:io';
import 'package:dio/dio.dart' as dio;
import 'package:http_parser/http_parser.dart';
import 'package:starchex/app/data/BPAccountModels.dart';
import '../../../../generated/locales.g.dart';
import '../../../shared/networking/BPRestAPI.dart';
import '../../../shared/account/BPSessionManager.dart';
import '../../../shared/components/BPEasyLoading.dart';
import '../../../shared/components/BPDeviceUtil.dart';
import '../../../data/BPCommonModels.dart';
import '../../../data/AssetVerificationModel.dart';
import '../../../routes/app_pages.dart';

enum AssetVerificationStep {
  assetList,
  uploadId,
  selectAssets,
  assetOperation,
  confirmation,
}

class AssetFormData {
  final AssetType type;
  String name;
  String? description;
  List<File> files;
  List<String> fileNames;
  bool isExpanded;
  bool isValid;
  bool isExistingData; // 标识是否为现有数据
  late final TextEditingController nameController;
  late final TextEditingController descriptionController;

  AssetFormData({
    required this.type,
    this.name = '',
    this.description,
    List<File>? files,
    List<String>? fileNames,
    this.isExpanded = true,
    this.isValid = false,
    this.isExistingData = false, // 默认为新数据
  })  : files = files ?? [],
        fileNames = fileNames ?? [] {
    nameController = TextEditingController(text: name);
    descriptionController = TextEditingController(text: description ?? '');

    // 监听控制器变化并同步到属性
    nameController.addListener(() {
      final newName = nameController.text;
      if (name != newName) {
        name = newName;
        updateValidation();
      }
    });

    descriptionController.addListener(() {
      final newDescription = descriptionController.text.trim().isEmpty ? null : descriptionController.text;
      if (description != newDescription) {
        description = newDescription;
      }
    });
  }

  // 检查表单是否有效
  void updateValidation() {
    if (isExistingData) {
      // 现有数据只要有名称就有效
      isValid = name.trim().isNotEmpty;
    } else {
      // 新数据需要名称和文件都有
      isValid = name.trim().isNotEmpty && files.isNotEmpty;
    }
  }

  // 格式化文件大小
  String getFileSize() {
    if (files.isEmpty) return '';
    final totalBytes = files.fold<int>(0, (sum, file) => sum + file.lengthSync());
    if (totalBytes < 1024) return '$totalBytes B';
    if (totalBytes < 1024 * 1024) return '${(totalBytes / 1024).toStringAsFixed(1)} KB';
    return '${(totalBytes / (1024 * 1024)).toStringAsFixed(1)} MB';
  }

  // 获取文件显示名称
  String getFileDisplayName() {
    if (fileNames.isEmpty) return '';
    if (fileNames.length == 1) return fileNames.first;
    return '${fileNames.length} files';
  }

  // 清理资源
  void dispose() {
    nameController.dispose();
    descriptionController.dispose();
  }
}

class AssetVerificationController extends GetxController {
  // 当前步骤
  final currentStep = AssetVerificationStep.assetList.obs;

  // 上传的ID文件
  final uploadedFile = Rxn<File>();
  final uploadedFileName = ''.obs;

  final uploadedIdCardUrl = Rxn<BPURLModel>();

  // 现有的ID卡片数据（从用户模型中获取）
  final existingIdCard = Rxn<BPURLModel>();
  final hasIdCardChanged = false.obs;

  // 当前正在操作的资产类型
  final currentAssetType = Rxn<AssetType>();

  // 所有资产类型的表单数据
  final allAssetForms = <AssetType, RxList<AssetFormData>>{}.obs;

  // 确认页面同意状态
  final hasAgreed = false.obs;

  // 当前类型的资产表单数据列表（计算属性）
  RxList<AssetFormData> get currentTypeAssetForms {
    if (currentAssetType.value == null) return <AssetFormData>[].obs;
    return allAssetForms[currentAssetType.value!] ?? <AssetFormData>[].obs;
  }

  @override
  void onInit() {
    super.onInit();
    _loadExistingIdCard();
    _initializeAssetForms();
    _loadExistingAssetVerifications();
    _checkAssetVerificationStatus();
  }

  /// 检查资产验证状态，如果已提交则跳转到审核页面
  void _checkAssetVerificationStatus() {
    final userModel = BPSessionManager.instance.accountSession?.accountModel;
    if (userModel?.financialStatus == BPFinancialStatus.submitted) {
      // 用户已经有资产验证数据，说明已经提交过，直接跳转到审核页面
      WidgetsBinding.instance.addPostFrameCallback((_) {
        Get.offNamed(Routes.ASSET_UNDER_REVIEW);
      });
    } else if (userModel?.financialStatus != null && userModel?.financialStatus != BPFinancialStatus.doNotAuthorize && userModel?.financialStatus != BPFinancialStatus.submitted) {
      // 用户已经通过资产验证，显示成功页面
      WidgetsBinding.instance.addPostFrameCallback((_) {
        Get.offNamed(Routes.ASSET_VERIFICATION_SUCCESS);
      });
    }
  }

  /// 检查用户是否已经提交了资产验证
  static bool hasSubmittedAssets() {
    final userModel = BPSessionManager.instance.accountSession?.accountModel;
    return userModel?.assetVerifications != null && userModel!.assetVerifications!.isNotEmpty;
  }

  @override
  void onClose() {
    // 清理所有AssetFormData中的TextEditingController
    for (final formsList in allAssetForms.values) {
      for (final form in formsList) {
        form.dispose();
      }
    }
    super.onClose();
  }

  /// 初始化所有资产类型的表单数据
  void _initializeAssetForms() {
    for (AssetType assetType in AssetType.values) {
      allAssetForms[assetType] = <AssetFormData>[].obs;
    }
  }

  /// 加载用户现有的资产验证数据
  void _loadExistingAssetVerifications() {
    final userModel = BPSessionManager.instance.accountSession?.accountModel;
    if (userModel?.assetVerifications != null && userModel!.assetVerifications!.isNotEmpty) {
      // 按资产类型分组现有的资产验证数据
      final Map<AssetType, List<AssetVerificationModel>> groupedAssets = {};

      for (final assetVerification in userModel.assetVerifications!) {
        if (assetVerification.type != null) {
          if (groupedAssets[assetVerification.type!] == null) {
            groupedAssets[assetVerification.type!] = [];
          }
          groupedAssets[assetVerification.type!]!.add(assetVerification);
        }
      }

      // 为每个资产类型创建表单数据
      for (final entry in groupedAssets.entries) {
        final assetType = entry.key;
        final assetList = entry.value;

        // 确保该资产类型有表单列表
        if (allAssetForms[assetType] == null) {
          allAssetForms[assetType] = <AssetFormData>[].obs;
        }

        // 为每个资产创建表单数据
        for (final asset in assetList) {
          final formData = AssetFormData(
            type: assetType,
            name: asset.name ?? '',
            description: asset.description,
            isExpanded: false, // 现有数据默认折叠
            isValid: true, // 现有数据标记为有效
            isExistingData: true, // 标记为现有数据
          );

          // 为现有数据创建虚拟文件信息（因为文件已经上传，我们只显示文件名）
          if (asset.proof != null && asset.proof!.isNotEmpty) {
            // 为每个proof创建一个虚拟文件名
            for (int i = 0; i < asset.proof!.length; i++) {
              // 生成显示用的文件名，可以基于资产名称和索引
              final fileName = asset.proof!.length > 1 ? '${asset.name ?? 'Asset'}_${i + 1}.pdf' : '${asset.name ?? 'Asset'}.pdf';
              formData.fileNames.add(fileName);

              // 注意：这里不添加实际的File对象，因为文件已经在服务器上
              // 我们需要在UI中区分现有文件和新上传的文件
            }
          }

          allAssetForms[assetType]!.add(formData);
        }
      }
    }
  }

  /// 获取指定资产类型的有效表单数量
  int getValidFormsCount(AssetType assetType) {
    final forms = allAssetForms[assetType];
    if (forms == null) return 0;
    return forms.where((form) => form.isValid).length;
  }

  /// 检查是否有任何有效的资产表单
  bool hasAnyValidAssetForms() {
    return AssetType.values.any((assetType) => getValidFormsCount(assetType) > 0);
  }

  /// 加载用户现有的ID卡片数据
  void _loadExistingIdCard() {
    final userModel = BPSessionManager.instance.accountSession?.accountModel;
    if (userModel?.idcard != null) {
      existingIdCard.value = userModel!.idcard;
      uploadedIdCardUrl.value = userModel.idcard;
      // 如果有现有数据，标记为已有文件但未修改
      hasIdCardChanged.value = false;
    }
  }

  /// 处理下一步操作
  Future<void> onNextPressed() async {
    if (currentStep.value == AssetVerificationStep.assetList) {
      // 从资产列表页面进入上传ID页面
      currentStep.value = AssetVerificationStep.uploadId;
    } else if (currentStep.value == AssetVerificationStep.uploadId) {
      // 从上传ID页面继续到选择资产页面
      if (existingIdCard.value != null && !hasIdCardChanged.value) {
        // 有现有数据且未修改，直接进入下一步
        currentStep.value = AssetVerificationStep.selectAssets;
      } else if (uploadedFile.value != null && hasIdCardChanged.value) {
        // 有新文件且已修改，需要上传并保存
        await _uploadAndSaveIdCard();
      } else if (existingIdCard.value == null) {
        // 没有现有数据，必须上传新文件
        BPEasyLoading.showError(LocaleKeys.asset_verification_error_no_file_selected_desc.tr);
      }
    } else if (currentStep.value == AssetVerificationStep.selectAssets) {
      // 从选择资产页面进入确认页面
      if (hasAnyValidAssetForms()) {
        currentStep.value = AssetVerificationStep.confirmation;
      } else {
        BPEasyLoading.showError(LocaleKeys.asset_verification_error_no_assets_added_desc.tr);
      }
    } else if (currentStep.value == AssetVerificationStep.confirmation) {
      // 最终提交所有资产
      if (hasAgreed.value) {
        await _submitAllAssetVerifications();
      } else {
        BPEasyLoading.showError(LocaleKeys.asset_verification_error_agreement_required_desc.tr);
      }
    } else if (currentStep.value == AssetVerificationStep.assetOperation) {
      // 从资产操作页面返回到选择资产页面
      currentStep.value = AssetVerificationStep.selectAssets;
    }
  }

  /// 返回上一步
  void onBackPressed() {
    if (currentStep.value == AssetVerificationStep.uploadId) {
      currentStep.value = AssetVerificationStep.assetList;
    } else if (currentStep.value == AssetVerificationStep.selectAssets) {
      currentStep.value = AssetVerificationStep.uploadId;
    } else if (currentStep.value == AssetVerificationStep.confirmation) {
      currentStep.value = AssetVerificationStep.selectAssets;
    } else if (currentStep.value == AssetVerificationStep.assetOperation) {
      // 从资产操作页面返回到选择资产页面
      currentStep.value = AssetVerificationStep.selectAssets;
    } else {
      Get.back();
    }
  }

  /// 切换同意状态
  void toggleAgreement() {
    hasAgreed.value = !hasAgreed.value;
  }

  /// 上传ID文件
  Future<void> uploadFile() async {
    try {
      BPDeviceUtil.pickPhotoFromAlbum(
        cropIfIO: false, // ID文件不需要裁剪
        completion: (BPFile? file) {
          if (file != null && file.file != null) {
            uploadedFile.value = file.file!;
            uploadedFileName.value = file.name;
            // 标记为已修改
            hasIdCardChanged.value = true;

            BPEasyLoading.showToast(
              LocaleKeys.asset_verification_success_file_selected_desc.trParams({'filename': file.name}),
            );
          }
        },
      );
    } catch (e) {
      BPEasyLoading.showError(
        LocaleKeys.asset_verification_error_file_selection_desc.trParams({'error': e.toString()}),
      );
    }
  }

  /// 点击资产类型卡片，进入资产操作页面
  void onAssetTypeClicked(AssetType assetType) {
    currentAssetType.value = assetType;
    _loadCurrentTypeAssets();
    currentStep.value = AssetVerificationStep.assetOperation;
  }

  /// 加载当前类型的资产表单数据
  void _loadCurrentTypeAssets() {
    if (currentAssetType.value == null) return;

    // 确保当前资产类型有表单列表
    if (allAssetForms[currentAssetType.value!] == null) {
      allAssetForms[currentAssetType.value!] = <AssetFormData>[].obs;
    }

    // 如果没有任何表单，添加一个空的表单
    // 注意：这里只为没有现有数据的情况添加空表单
    if (allAssetForms[currentAssetType.value!]!.isEmpty) {
      addNewAssetForm();
    }
  }

  /// 添加新的资产表单
  void addNewAssetForm() {
    if (currentAssetType.value == null) return;

    final newForm = AssetFormData(
      type: currentAssetType.value!,
      isExpanded: true,
    );

    // 确保当前资产类型有表单列表
    if (allAssetForms[currentAssetType.value!] == null) {
      allAssetForms[currentAssetType.value!] = <AssetFormData>[].obs;
    }

    allAssetForms[currentAssetType.value!]!.add(newForm);
  }

  /// 删除资产表单
  void removeAssetForm(int index) {
    if (currentAssetType.value == null) return;

    final forms = allAssetForms[currentAssetType.value!];
    if (forms != null && index >= 0 && index < forms.length) {
      final form = forms[index];

      // 如果是现有数据，不允许删除
      if (form.isExistingData) {
        BPEasyLoading.showError(LocaleKeys.asset_verification_error_cannot_delete_existing_asset_desc.tr);
        return;
      }

      // 先清理要删除的表单的控制器
      forms[index].dispose();
      forms.removeAt(index);
      // 如果删除后没有表单了，至少保留一个空表单
      if (forms.isEmpty) {
        addNewAssetForm();
      }
    }
  }

  /// 切换表单展开状态
  void toggleFormExpanded(int index) {
    if (currentAssetType.value == null) return;

    final forms = allAssetForms[currentAssetType.value!];
    if (forms != null && index >= 0 && index < forms.length) {
      forms[index].isExpanded = !forms[index].isExpanded;
      forms.refresh();
    }
  }

  /// 更新资产名称（现在通过TextEditingController的监听器自动处理）
  void updateAssetName(int index, String name) {
    // 这个方法现在由TextEditingController的监听器自动调用
    // 保留这个方法以防有其他地方需要手动更新
    if (currentAssetType.value == null) return;

    final forms = allAssetForms[currentAssetType.value!];
    if (forms != null && index >= 0 && index < forms.length) {
      forms[index].nameController.text = name;
      // 不需要手动调用updateValidation，监听器会处理
    }
  }

  /// 更新资产描述（现在通过TextEditingController的监听器自动处理）
  void updateAssetDescription(int index, String description) {
    // 这个方法现在由TextEditingController的监听器自动调用
    // 保留这个方法以防有其他地方需要手动更新
    if (currentAssetType.value == null) return;

    final forms = allAssetForms[currentAssetType.value!];
    if (forms != null && index >= 0 && index < forms.length) {
      forms[index].descriptionController.text = description;
      // 不需要手动更新，监听器会处理
    }
  }

  /// 为指定表单选择文件
  Future<void> selectFilesForForm(int index, {bool replaceExisting = false}) async {
    if (currentAssetType.value == null) return;

    final forms = allAssetForms[currentAssetType.value!];
    if (forms == null || index < 0 || index >= forms.length) return;

    try {
      // 根据资产类型选择不同的文件类型
      List<String> allowedExtensions;
      switch (currentAssetType.value!) {
        case AssetType.realEstate:
        case AssetType.financialAssets:
        case AssetType.others:
          allowedExtensions = ['jpg', 'jpeg', 'png', 'pdf'];
          break;
        case AssetType.cryptoCurrency:
          allowedExtensions = ['jpg', 'jpeg', 'png', 'pdf', 'txt'];
          break;
      }

      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: allowedExtensions,
        allowMultiple: true,
      );

      if (result != null && result.files.isNotEmpty) {
        final form = forms[index];

        // 如果需要替换现有文件，则清空；否则追加
        if (replaceExisting) {
          form.files.clear();
          form.fileNames.clear();
        }

        int newFilesCount = 0;
        for (var file in result.files) {
          if (file.path != null) {
            // 检查是否已存在同名文件
            if (!form.fileNames.contains(file.name)) {
              form.files.add(File(file.path!));
              form.fileNames.add(file.name);
              newFilesCount++;
            }
          }
        }

        form.updateValidation();
        forms.refresh();

        if (newFilesCount > 0) {
          BPEasyLoading.showToast(
            LocaleKeys.asset_verification_success_files_added_desc.trParams({'count': newFilesCount.toString(), 'total': form.files.length.toString()}),
          );
        } else {
          BPEasyLoading.showToast(
            LocaleKeys.asset_verification_info_files_already_exist_desc.tr,
          );
        }
      }
    } catch (e) {
      BPEasyLoading.showError(
        LocaleKeys.asset_verification_error_files_selection_desc.trParams({'error': e.toString()}),
      );
    }
  }

  /// 移除表单中的指定文件
  void removeFileFromForm(int formIndex, int fileIndex) {
    if (currentAssetType.value == null) return;

    final forms = allAssetForms[currentAssetType.value!];
    if (forms != null && formIndex >= 0 && formIndex < forms.length) {
      final form = forms[formIndex];

      // 如果是现有数据，不允许删除文件
      if (form.isExistingData) {
        BPEasyLoading.showError(LocaleKeys.asset_verification_error_cannot_delete_existing_file_desc.tr);
        return;
      }

      if (fileIndex >= 0 && fileIndex < form.files.length) {
        form.files.removeAt(fileIndex);
        form.fileNames.removeAt(fileIndex);
        form.updateValidation();
        forms.refresh();
      }
    }
  }

  /// 检查是否有有效的资产
  bool hasValidAssets() {
    return hasAnyValidAssetForms();
  }

  /// 获取资产类型图标
  String getAssetTypeIcon(AssetType type) {
    switch (type) {
      case AssetType.realEstate:
        return 'assets_verify_estate.svg';
      case AssetType.financialAssets:
        return 'assets_verify_financial.svg';
      case AssetType.cryptoCurrency:
        return 'assets_verify_crypto.svg';
      case AssetType.others:
        return 'assets_verify_others.svg';
    }
  }

  /// 获取资产类型名称
  String getAssetTypeName(AssetType type) {
    switch (type) {
      case AssetType.realEstate:
        return LocaleKeys.asset_verification_asset_type_real_estate.tr;
      case AssetType.financialAssets:
        return LocaleKeys.asset_verification_asset_type_financial_assets.tr;
      case AssetType.cryptoCurrency:
        return LocaleKeys.asset_verification_asset_type_crypto_currency.tr;
      case AssetType.others:
        return LocaleKeys.asset_verification_asset_type_other_assets.tr;
    }
  }

  /// 检查是否可以继续下一步
  bool get canProceedFromUpload => (existingIdCard.value != null) || (uploadedFile.value != null);
  bool get canProceedFromAssets => hasValidAssets();

  /// 上传并保存ID卡片
  Future<void> _uploadAndSaveIdCard() async {
    if (uploadedFile.value == null) return;

    try {
      BPEasyLoading.show();

      // 1. 先上传文件
      final uploadRes = await BPRestClient().uploadMultipartFiles(files: [
        await dio.MultipartFile.fromFile(
          uploadedFile.value!.path,
          filename: uploadedFileName.value,
          contentType: MediaType.parse(_getContentType(uploadedFileName.value)),
        ),
      ]);

      // 2. 保存ID卡片信息到用户
      final idCardUrl = BPURLModel(id: uploadRes.first.id);
      final userId = BPSessionManager.instance.accountSession?.accountModel.id;
      if (userId != null) {
        await BPRestClient().editMeInfo(
          id: userId,
          body: {
            'idcard': idCardUrl.toJson(),
          },
        );
      }

      uploadedIdCardUrl.value = idCardUrl;

      // 3. 更新本地会话信息
      final userModel = BPSessionManager.instance.accountSession?.accountModel;
      if (userModel != null) {
        BPSessionManager.instance.synchronizeAccountBaseInfo(userModel);
      }

      BPEasyLoading.dismiss();

      BPEasyLoading.showToast(
        LocaleKeys.asset_verification_success_file_selected_desc.trParams({'filename': uploadedFileName.value}),
      );

      // 4. 进入下一步
      currentStep.value = AssetVerificationStep.selectAssets;
    } catch (e) {
      BPEasyLoading.dismiss();
      catchedError(e);
    }
  }

  /// 提交所有资产验证
  Future<void> _submitAllAssetVerifications() async {
    try {
      // 显示加载对话框
      BPEasyLoading.show();

      // 收集所有资产验证数据
      List<Map<String, dynamic>> assetVerifications = [];

      // 处理所有资产类型

      for (AssetType assetType in AssetType.values) {
        final forms = allAssetForms[assetType];
        if (forms == null) continue;

        final validForms = forms.where((form) => form.isValid).toList();

        for (final form in validForms) {
          for (int i = 0; i < form.files.length; i++) {
            final file = form.files[i];

            // 上传资产文件
            final uploadRes = await BPRestClient().uploadFileDetail(
              file: file,
            );

            // 创建资产验证数据
            final assetVerification = AssetVerificationModel(
              type: form.type,
              name: form.files.length > 1 ? '${form.name} (${i + 1})' : form.name,
              description: form.description,
              proof: [BPURLModel(id: uploadRes.first.id)],
            );

            // 添加到数组中
            assetVerifications.add(assetVerification.toJson());
          }
        }
      }

      // 提交资产验证数据

      // 一次性提交所有资产验证数据
      if (assetVerifications.isNotEmpty) {
        await BPRestClient().addAssetVerification(body: {
          'assetVerifications': assetVerifications,
        });
      }

      // 确认资产

      // 调用确认资产 API
      await BPRestClient().confirmAssets();

      // 计算总的有效表单数量
      int totalValidForms = AssetType.values.fold(0, (sum, assetType) => sum + getValidFormsCount(assetType));

      // 隐藏加载对话框
      BPEasyLoading.dismiss();

      // 显示成功消息
      BPEasyLoading.showSuccess(
        LocaleKeys.asset_verification_success_assets_submitted_desc.trParams({'count': totalValidForms.toString()}),
      );

      // 提交成功后导航到审核页面
      Get.offNamed(Routes.ASSET_UNDER_REVIEW);
    } catch (e) {
      // 隐藏加载对话框
      BPEasyLoading.dismiss();
      catchedError(e);
    }
  }

  /// 获取文件的Content-Type
  String _getContentType(String fileName) {
    final extension = fileName.split('.').last.toLowerCase();
    switch (extension) {
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      case 'pdf':
        return 'application/pdf';
      case 'txt':
        return 'text/plain';
      default:
        return 'application/octet-stream';
    }
  }

  /// 检查是否可以保存当前资产类型
  bool get canSaveCurrentAssets {
    if (currentAssetType.value == null) return false;
    final forms = allAssetForms[currentAssetType.value!];
    if (forms == null) return false;
    return forms.any((form) => form.isValid);
  }

  /// 保存当前资产类型的所有有效表单
  Future<void> saveCurrentAssets() async {
    if (currentAssetType.value == null) return;

    final forms = allAssetForms[currentAssetType.value!];
    if (forms == null) return;

    final validForms = forms.where((form) => form.isValid).toList();
    if (validForms.isEmpty) {
      BPEasyLoading.showError(LocaleKeys.asset_verification_error_no_valid_assets_desc.tr);
      return;
    }

    try {
      // 显示加载对话框
      BPEasyLoading.show();

      // 收集所有资产验证数据
      List<Map<String, dynamic>> assetVerifications = [];

      for (final form in validForms) {
        for (int i = 0; i < form.files.length; i++) {
          final file = form.files[i];

          // 上传资产文件
          final uploadRes = await BPRestClient().uploadFileDetail(
            file: file,
          );

          // 创建资产验证数据
          final assetVerification = AssetVerificationModel(
            type: form.type,
            name: form.files.length > 1 ? '${form.name} (${i + 1})' : form.name,
            description: form.description,
            proof: [BPURLModel(id: uploadRes.first.id)],
          );

          // 添加到数组中
          assetVerifications.add(assetVerification.toJson());
        }
      }

      // 提交资产验证数据

      // 一次性提交所有资产验证数据
      await BPRestClient().addAssetVerification(body: {
        'assetVerifications': assetVerifications,
      });

      // 隐藏加载对话框
      BPEasyLoading.dismiss();

      // 显示成功消息
      BPEasyLoading.showSuccess(
        LocaleKeys.asset_verification_success_assets_saved_desc.trParams({
          'count': validForms.length.toString(),
          'type': getAssetTypeName(currentAssetType.value!),
        }),
      );

      // 保存成功后返回到选择资产页面
      currentStep.value = AssetVerificationStep.selectAssets;
    } catch (e) {
      // 隐藏加载对话框
      BPEasyLoading.dismiss();
      catchedError(e);
    }
  }
}
